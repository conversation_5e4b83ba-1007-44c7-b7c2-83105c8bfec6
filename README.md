# AI-SQLREVIEW
基于Ant Design Vue开发

## 环境
* node -- 运行/编译
* yarn -- 依赖管理
* webpack -- 打包
* eslint -- 代码规约
* vue-cli -- 构建
## 安装
克隆项目到本地:
```
$ git clone ssh://********************:8010/ludba_pro/aisqlreview-web.git
```
安装依赖：
```
$ npm install
```
## 启动
```
$ npm start
```
## 规范
* 文件命名采用驼峰，组件首字母大写以作区分
* 引用外部文件采用别名 @/utils/xx，局部文件可采用相对路径../components/yy
* components/ 存放基础组件，components/Biz/ 专门存放业务相关组件
* 页面和组件样式必须添加**scoped**，防止互相影响；全局样式，特别是覆盖样式谨慎修改，可选取 > 选择器防止影响深层级样式
* 页面根据菜单分级，跟路由对应；不要将页面散列放到**pages**目录中，如**配置模块**可以定义为：
  ```
  config
    project  （对应路由/config/project）
    dataSource （对应路由/config/data-source）
  ```
* 项目目前采用vscode右键格式化文档，上传前必须格式化代码并保证编译无warning和error

## 注意事项
* 工程配置了mock功能，在mock目录中按功能创建自己的目录
* 通过mixin设置了全屏loading函数$showLoading、$hideLoading（用于提交操作使用）
* 多阅读antd-Design文档，多熟悉项目代码，防止重复造轮子

## Eslint config
```
{
    "files.associations": {
        "*.cjson": "jsonc",
        "*.wxss": "css",
        "*.wxs": "javascript"
    },
    "emmet.includeLanguages": {
        "wxml": "html"
    },
    "minapp-vscode.disableAutoConfig": true,
    "workbench.tree.indent": 16,
    "javascript.updateImportsOnFileMove.enabled": "always",
    "explorer.confirmDelete": false,
    "workbench.sideBar.location": "left",
    "window.zoomLevel": -1,
    "editor.fontSize": 14,
    "terminal.integrated.fontSize": 14,
    "editor.tabSize": 2,
    "editor.detectIndentation": false
}
```


